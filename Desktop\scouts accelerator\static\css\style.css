/* Custom CSS for Scouts Accelerator */

:root {
    --primary-color: #0d6efd;
    --secondary-color: #6c757d;
    --success-color: #198754;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --info-color: #0dcaf0;
    --light-color: #f8f9fa;
    --dark-color: #212529;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
}

/* Navigation */
.navbar-brand {
    font-size: 1.5rem;
}

.navbar-nav .nav-link {
    font-weight: 500;
    transition: color 0.3s ease;
}

.navbar-nav .nav-link:hover {
    color: var(--warning-color) !important;
}

/* Hero Section */
.hero-section {
    background: linear-gradient(135deg, var(--primary-color) 0%, #0056b3 100%);
    min-height: 100vh;
    display: flex;
    align-items: center;
}

.hero-image .card {
    transition: transform 0.3s ease;
    border: none;
    border-radius: 10px;
}

.hero-image .card:hover {
    transform: translateY(-5px);
}

/* Feature Cards */
.feature-icon {
    transition: transform 0.3s ease;
}

.card:hover .feature-icon {
    transform: scale(1.1);
}

.card {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    border-radius: 15px;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0,0,0,0.1) !important;
}

/* Dashboard Styles */
.dashboard-card {
    border-radius: 15px;
    border: none;
    transition: all 0.3s ease;
}

.dashboard-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 20px rgba(0,0,0,0.1);
}

.stat-card {
    background: linear-gradient(135deg, var(--primary-color), #0056b3);
    color: white;
    border-radius: 15px;
    padding: 2rem;
}

.stat-number {
    font-size: 2.5rem;
    font-weight: bold;
}

/* Progress Bars */
.progress {
    height: 10px;
    border-radius: 10px;
}

.progress-bar {
    border-radius: 10px;
}

/* Buttons */
.btn {
    border-radius: 10px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.2);
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-color), #0056b3);
    border: none;
}

.btn-warning {
    background: linear-gradient(135deg, var(--warning-color), #e0a800);
    border: none;
    color: var(--dark-color);
}

/* Forms */
.form-control {
    border-radius: 10px;
    border: 2px solid #e9ecef;
    transition: border-color 0.3s ease;
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

.input-group-text {
    border-radius: 10px 0 0 10px;
    border: 2px solid #e9ecef;
    border-right: none;
    background-color: var(--light-color);
}

/* Rank Cards */
.rank-card {
    border-radius: 15px;
    border: none;
    margin-bottom: 1rem;
    transition: all 0.3s ease;
}

.rank-card:hover {
    box-shadow: 0 8px 20px rgba(0,0,0,0.1);
}

.rank-header {
    background: linear-gradient(135deg, var(--primary-color), #0056b3);
    color: white;
    border-radius: 15px 15px 0 0;
    padding: 1rem;
}

.requirement-item {
    border-left: 4px solid var(--primary-color);
    padding: 1rem;
    margin: 0.5rem 0;
    background-color: var(--light-color);
    border-radius: 0 10px 10px 0;
    transition: all 0.3s ease;
}

.requirement-item:hover {
    background-color: #e9ecef;
    transform: translateX(5px);
}

.requirement-completed {
    border-left-color: var(--success-color);
    background-color: #d1e7dd;
}

/* Badges */
.badge {
    border-radius: 10px;
    font-size: 0.8rem;
    padding: 0.5rem 1rem;
}

/* Loading Spinner */
.spinner-border {
    width: 3rem;
    height: 3rem;
}

/* Chat Interface */
.chat-container {
    height: 500px;
    overflow-y: auto;
    border: 2px solid #e9ecef;
    border-radius: 15px;
    padding: 1rem;
    background-color: var(--light-color);
}

.chat-message {
    margin-bottom: 1rem;
    padding: 1rem;
    border-radius: 15px;
    max-width: 80%;
}

.chat-message.user {
    background-color: var(--primary-color);
    color: white;
    margin-left: auto;
    border-bottom-right-radius: 5px;
}

.chat-message.ai {
    background-color: white;
    border: 2px solid #e9ecef;
    border-bottom-left-radius: 5px;
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero-section {
        text-align: center;
    }
    
    .display-4 {
        font-size: 2rem;
    }
    
    .stat-number {
        font-size: 2rem;
    }
    
    .dashboard-card {
        margin-bottom: 1rem;
    }
}

/* Animation Classes */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.slide-in-left {
    animation: slideInLeft 0.5s ease-out;
}

@keyframes slideInLeft {
    from { opacity: 0; transform: translateX(-50px); }
    to { opacity: 1; transform: translateX(0); }
}

.slide-in-right {
    animation: slideInRight 0.5s ease-out;
}

@keyframes slideInRight {
    from { opacity: 0; transform: translateX(50px); }
    to { opacity: 1; transform: translateX(0); }
}

/* Custom Scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: var(--light-color);
    border-radius: 10px;
}

::-webkit-scrollbar-thumb {
    background: var(--primary-color);
    border-radius: 10px;
}

::-webkit-scrollbar-thumb:hover {
    background: #0056b3;
}
