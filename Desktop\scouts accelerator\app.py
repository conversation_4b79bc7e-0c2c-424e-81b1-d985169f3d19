from flask import Flask, render_template, request, redirect, url_for, session, flash, jsonify
import sqlite3
import hashlib
import secrets
import smtplib
from email.mime.text import MimeText
from email.mime.multipart import MimeMultipart
import os
from datetime import datetime, timedelta
import requests
import json

app = Flask(__name__)
app.secret_key = secrets.token_hex(16)

# Database initialization
def init_db():
    conn = sqlite3.connect('scouts.db')
    cursor = conn.cursor()
    
    # Users table
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS users (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            username TEXT UNIQUE NOT NULL,
            email TEXT UNIQUE NOT NULL,
            password_hash TEXT NOT NULL,
            name TEXT NOT NULL,
            is_verified INTEGER DEFAULT 0,
            verification_token TEXT,
            user_type TEXT DEFAULT 'scout',
            current_rank_id INTEGER,
            troop_id INTEGER,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (current_rank_id) REFERENCES ranks (id),
            FOREIGN KEY (troop_id) REFERENCES troops (id)
        )
    ''')
    
    # Ranks table
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS ranks (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL,
            order_index INTEGER NOT NULL,
            created_by INTEGER,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (created_by) REFERENCES users (id)
        )
    ''')
    
    # Requirements table
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS requirements (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            rank_id INTEGER NOT NULL,
            title TEXT NOT NULL,
            description TEXT,
            created_by INTEGER,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (rank_id) REFERENCES ranks (id),
            FOREIGN KEY (created_by) REFERENCES users (id)
        )
    ''')
    
    # Lessons table
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS lessons (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            requirement_id INTEGER NOT NULL,
            title TEXT NOT NULL,
            html_content TEXT,
            css_content TEXT,
            created_by INTEGER,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (requirement_id) REFERENCES requirements (id),
            FOREIGN KEY (created_by) REFERENCES users (id)
        )
    ''')
    
    # Troops table
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS troops (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL,
            scoutmaster_id INTEGER,
            created_by INTEGER,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (scoutmaster_id) REFERENCES users (id),
            FOREIGN KEY (created_by) REFERENCES users (id)
        )
    ''')
    
    # User progress table
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS user_progress (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            user_id INTEGER NOT NULL,
            requirement_id INTEGER NOT NULL,
            status TEXT DEFAULT 'not_started',
            completed_lesson INTEGER DEFAULT 0,
            quiz_passed INTEGER DEFAULT 0,
            signed_off INTEGER DEFAULT 0,
            signed_off_by INTEGER,
            signed_off_date TIMESTAMP,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users (id),
            FOREIGN KEY (requirement_id) REFERENCES requirements (id),
            FOREIGN KEY (signed_off_by) REFERENCES users (id)
        )
    ''')
    
    # Sign-off requests table
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS signoff_requests (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            user_id INTEGER NOT NULL,
            requirement_id INTEGER NOT NULL,
            eligible_scout_id INTEGER,
            status TEXT DEFAULT 'pending',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            accepted_at TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users (id),
            FOREIGN KEY (requirement_id) REFERENCES requirements (id),
            FOREIGN KEY (eligible_scout_id) REFERENCES users (id)
        )
    ''')
    
    # Cabinet roles table
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS cabinet_roles (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            user_id INTEGER NOT NULL,
            role_name TEXT NOT NULL,
            assigned_by INTEGER,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users (id),
            FOREIGN KEY (assigned_by) REFERENCES users (id)
        )
    ''')
    
    # Eligible scouts table
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS eligible_scouts (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            user_id INTEGER NOT NULL,
            requirement_id INTEGER NOT NULL,
            assigned_by INTEGER,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users (id),
            FOREIGN KEY (requirement_id) REFERENCES requirements (id),
            FOREIGN KEY (assigned_by) REFERENCES users (id)
        )
    ''')
    
    conn.commit()
    conn.close()

# Initialize database on startup
init_db()

# Create founder account if it doesn't exist
def create_founder_account():
    conn = sqlite3.connect('scouts.db')
    cursor = conn.cursor()
    
    cursor.execute('SELECT id FROM users WHERE username = ?', ('shaurya',))
    if not cursor.fetchone():
        password_hash = hashlib.sha256('EAGLESCOUT'.encode()).hexdigest()
        cursor.execute('''
            INSERT INTO users (username, email, password_hash, name, is_verified, user_type)
            VALUES (?, ?, ?, ?, ?, ?)
        ''', ('shaurya', '<EMAIL>', password_hash, 'Founder', 1, 'founder'))
        conn.commit()
    
    conn.close()

create_founder_account()

@app.route('/')
def index():
    return render_template('index.html')

@app.route('/login', methods=['GET', 'POST'])
def login():
    if request.method == 'POST':
        username = request.form['username']
        password = request.form['password']
        password_hash = hashlib.sha256(password.encode()).hexdigest()
        
        conn = sqlite3.connect('scouts.db')
        cursor = conn.cursor()
        cursor.execute('SELECT id, username, user_type, is_verified FROM users WHERE username = ? AND password_hash = ?', 
                      (username, password_hash))
        user = cursor.fetchone()
        conn.close()
        
        if user and user[3]:  # Check if verified
            session['user_id'] = user[0]
            session['username'] = user[1]
            session['user_type'] = user[2]
            
            if user[2] == 'founder':
                return redirect(url_for('founder_dashboard'))
            else:
                return redirect(url_for('dashboard'))
        else:
            flash('Invalid credentials or unverified account')
    
    return render_template('login.html')

@app.route('/signup', methods=['GET', 'POST'])
def signup():
    if request.method == 'POST':
        name = request.form['name']
        email = request.form['email']
        username = request.form['username']
        password = request.form['password']
        
        password_hash = hashlib.sha256(password.encode()).hexdigest()
        verification_token = secrets.token_urlsafe(32)
        
        conn = sqlite3.connect('scouts.db')
        cursor = conn.cursor()
        
        try:
            cursor.execute('''
                INSERT INTO users (username, email, password_hash, name, verification_token)
                VALUES (?, ?, ?, ?, ?)
            ''', (username, email, password_hash, name, verification_token))
            conn.commit()
            
            # Send verification email (simulated for local development)
            flash(f'Account created! Verification link: /verify/{verification_token}')
            return redirect(url_for('login'))
            
        except sqlite3.IntegrityError:
            flash('Username or email already exists')
        finally:
            conn.close()
    
    return render_template('signup.html')

@app.route('/verify/<token>')
def verify_email(token):
    conn = sqlite3.connect('scouts.db')
    cursor = conn.cursor()
    cursor.execute('UPDATE users SET is_verified = 1, verification_token = NULL WHERE verification_token = ?', (token,))
    
    if cursor.rowcount > 0:
        conn.commit()
        flash('Email verified successfully! You can now log in.')
    else:
        flash('Invalid verification token')
    
    conn.close()
    return redirect(url_for('login'))

@app.route('/dashboard')
def dashboard():
    if 'user_id' not in session:
        return redirect(url_for('login'))
    
    return render_template('dashboard.html')

@app.route('/founder_dashboard')
def founder_dashboard():
    if 'user_id' not in session or session.get('user_type') != 'founder':
        return redirect(url_for('login'))
    
    return render_template('founder_dashboard.html')

@app.route('/logout')
def logout():
    session.clear()
    return redirect(url_for('index'))

if __name__ == '__main__':
    app.run(debug=True, port=5000)
